-- Main server-side functionality for valic_housing

local QBCore = exports['qb-core']:GetCoreObject()

-- Define a safe locale function that doesn't rely on the global _ function
local function GetLocaleString(key, ...)
    -- Define common translations
    local translations = {
        ['notification_title'] = 'Valic Housing',
        ['enter_password'] = '<PERSON>adejte heslo',
        ['incorrect_password'] = 'Nesprávné heslo',
        ['password_attempts_remaining'] = 'Zbývající pokusy: %d',
        ['door_locked'] = 'Dveře jsou uzamčeny na %d minut',
        ['tour_time_remaining'] = 'Zbývající čas prohlídky: %d minut',
        ['tour_code'] = 'Kód prohlídky: %s',
        ['property_purchased'] = 'Nemovitost zakoupena za $%s',
        ['property_sold'] = 'Nemovitost prodána za $%s',
        ['property_transferred'] = 'Nemovitost přepsána na jiného majitele',
        ['not_enough_money'] = 'Nemáte dostatek peněz',
        ['password_requirements'] = 'Heslo musí mít %d-%d alfanumerick<PERSON>ch znak<PERSON>',
        ['keys_revoked'] = 'Klíče odebrány: %s',
        ['keys_shared'] = 'Klíče sdíleny s: %s',
        ['tour_started'] = 'Prohlídka začala. Kód: %s. Zbývající čas: %d minut',
        ['tour_ended'] = 'Prohlídka skončila'
    }

    local translation = translations[key]

    if not translation then
        return 'Missing translation: ' .. key
    end

    if ... then
        return string.format(translation, ...)
    end

    return translation
end

-- For backward compatibility, define _ as an alias to GetLocaleString
_ = GetLocaleString

-- Track password attempts
local passwordAttempts = {}

-- Initialize password attempts tracking
local function InitializePasswordAttempts()
    local houses = GetAllHouses()
    for _, house in pairs(houses) do
        passwordAttempts[house.id] = {
            count = 0,
            lockedUntil = 0
        }
    end
end

-- Check if a house is locked due to too many password attempts
local function IsHouseLocked(houseId)
    if not passwordAttempts[houseId] then
        passwordAttempts[houseId] = {
            count = 0,
            lockedUntil = 0
        }
    end

    local currentTime = os.time()
    return passwordAttempts[houseId].lockedUntil > currentTime
end

-- Reset password attempts for a house
local function ResetPasswordAttempts(houseId)
    if passwordAttempts[houseId] then
        passwordAttempts[houseId].count = 0
        passwordAttempts[houseId].lockedUntil = 0
    end
end

-- Increment password attempts for a house
local function IncrementPasswordAttempts(houseId)
    if not passwordAttempts[houseId] then
        passwordAttempts[houseId] = {
            count = 0,
            lockedUntil = 0
        }
    end

    passwordAttempts[houseId].count = passwordAttempts[houseId].count + 1

    -- If max attempts reached, lock the house
    if passwordAttempts[houseId].count >= Config.MaxPasswordAttempts then
        passwordAttempts[houseId].lockedUntil = os.time() + (Config.PasswordLockoutTime * 60)
        passwordAttempts[houseId].count = 0
        return true
    end

    return false
end

-- Get remaining password attempts for a house
local function GetRemainingAttempts(houseId)
    if not passwordAttempts[houseId] then
        passwordAttempts[houseId] = {
            count = 0,
            lockedUntil = 0
        }
    end

    return Config.MaxPasswordAttempts - passwordAttempts[houseId].count
end

-- Get lockout time remaining in seconds
local function GetLockoutTimeRemaining(houseId)
    if not passwordAttempts[houseId] then return 0 end

    local currentTime = os.time()
    local remainingTime = passwordAttempts[houseId].lockedUntil - currentTime

    return remainingTime > 0 and remainingTime or 0
end

-- Check if a player is the owner of a house
local function IsHouseOwner(houseId, citizenId)
    local house = GetHouseById(houseId)
    return house and house.owner_citizenid == citizenId
end

-- Check if a player has keys to a house
local function HasHouseKeys(houseId, citizenId)
    local house = GetHouseById(houseId)
    if not house or not house.key_holders then return false end

    local keyHolders = json.decode(house.key_holders) or {}

    for _, holder in ipairs(keyHolders) do
        if holder == citizenId then
            return true
        end
    end

    return false
end

-- Check if a player can access a house (owner, key holder, or valid tour)
local function CanAccessHouse(houseId, citizenId, tourCode)
    -- Check if player is owner
    if IsHouseOwner(houseId, citizenId) then
        return true, "owner"
    end

    -- Check if player has keys
    if HasHouseKeys(houseId, citizenId) then
        return true, "key_holder"
    end

    -- Check if player has valid tour code
    if tourCode and IsTourValid(houseId, tourCode) then
        return true, "tour"
    end

    return false, nil
end

-- Get nearby players for key sharing
local function GetNearbyPlayers(playerId)
    local player = QBCore.Functions.GetPlayer(playerId)
    if not player then return {} end

    local playerCoords = GetEntityCoords(GetPlayerPed(playerId))
    local players = QBCore.Functions.GetPlayers()
    local nearbyPlayers = {}

    for _, targetId in ipairs(players) do
        if tonumber(targetId) ~= tonumber(playerId) then
            local targetCoords = GetEntityCoords(GetPlayerPed(targetId))
            local distance = #(playerCoords - targetCoords)

            if distance <= 5.0 then
                local targetPlayer = QBCore.Functions.GetPlayer(targetId)
                if targetPlayer then
                    table.insert(nearbyPlayers, {
                        id = targetId,
                        citizenId = targetPlayer.PlayerData.citizenid,
                        name = targetPlayer.PlayerData.charinfo.firstname .. ' ' .. targetPlayer.PlayerData.charinfo.lastname
                    })
                end
            end
        end
    end

    return nearbyPlayers
end

-- Initialize when resource starts
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Load server-side configuration
        local success, config = pcall(function() return require('server/config') end)
        if success then
            print('^2[valic_housing]^7 Server configuration loaded successfully')
            if config.Debug then
                print('^3[valic_housing]^7 Debug mode enabled')
                print('^3[valic_housing]^7 Webhook URL: ' .. (config.WebhookURL ~= '' and 'Set' or 'Not set'))
                print('^3[valic_housing]^7 Log level: ' .. config.LogLevel)
            end
        else
            print('^1[ERROR]^7 Failed to load server config: ' .. tostring(config))
        end

        -- Initialize password attempts
        InitializePasswordAttempts()
    end
end)

-- Callbacks and events

-- Get all houses
QBCore.Functions.CreateCallback('valic_housing:server:GetAllHouses', function(source, cb)
    local houses = GetAllHouses()
    cb(houses)
end)

-- Get house by ID
QBCore.Functions.CreateCallback('valic_housing:server:GetHouseById', function(source, cb, houseId)
    local house = GetHouseById(houseId)
    cb(house)
end)

-- Get houses owned by player
QBCore.Functions.CreateCallback('valic_housing:server:GetOwnedHouses', function(source, cb)
    local player = QBCore.Functions.GetPlayer(source)
    if not player then return cb({}) end

    local citizenId = player.PlayerData.citizenid
    local houses = GetHousesByCitizenId(citizenId)
    cb(houses)
end)

-- Get houses where player has keys
QBCore.Functions.CreateCallback('valic_housing:server:GetHousesWithKeys', function(source, cb)
    local player = QBCore.Functions.GetPlayer(source)
    if not player then return cb({}) end

    local citizenId = player.PlayerData.citizenid
    local houses = GetHousesWithKeys(citizenId)
    cb(houses)
end)

-- Check if player can access house
QBCore.Functions.CreateCallback('valic_housing:server:CanAccessHouse', function(source, cb, houseId, tourCode)
    local player = QBCore.Functions.GetPlayer(source)
    if not player then return cb(false) end

    local citizenId = player.PlayerData.citizenid
    local canAccess, accessType = CanAccessHouse(houseId, citizenId, tourCode)
    cb(canAccess, accessType)
end)

-- Check password
QBCore.Functions.CreateCallback('valic_housing:server:CheckPassword', function(source, cb, houseId, password)
    -- Check if house is locked due to too many attempts
    if IsHouseLocked(houseId) then
        local remainingTime = math.ceil(GetLockoutTimeRemaining(houseId) / 60)
        return cb(false, 'locked', remainingTime)
    end

    local house = GetHouseById(houseId)
    if not house or not house.password then
        return cb(false, 'no_password')
    end

    if house.password == password then
        ResetPasswordAttempts(houseId)
        return cb(true)
    else
        local locked = IncrementPasswordAttempts(houseId)
        if locked then
            return cb(false, 'locked', Config.PasswordLockoutTime)
        else
            local remainingAttempts = GetRemainingAttempts(houseId)
            return cb(false, 'incorrect', remainingAttempts)
        end
    end
end)

-- Get nearby players for key sharing
QBCore.Functions.CreateCallback('valic_housing:server:GetNearbyPlayers', function(source, cb)
    local nearbyPlayers = GetNearbyPlayers(source)
    cb(nearbyPlayers)
end)

-- Check if a house is locked due to too many password attempts
QBCore.Functions.CreateCallback('valic_housing:server:IsHouseLocked', function(source, cb, houseId)
    cb(IsHouseLocked(houseId))
end)

-- Get lockout time remaining
QBCore.Functions.CreateCallback('valic_housing:server:GetLockoutTimeRemaining', function(source, cb, houseId)
    cb(GetLockoutTimeRemaining(houseId))
end)

-- Check if a player is the owner of a house
QBCore.Functions.CreateCallback('valic_housing:server:IsHouseOwner', function(source, cb, houseId, citizenId)
    cb(IsHouseOwner(houseId, citizenId))
end)

-- Check if a player has keys to a house
QBCore.Functions.CreateCallback('valic_housing:server:HasHouseKeys', function(source, cb, houseId, citizenId)
    cb(HasHouseKeys(houseId, citizenId))
end)

-- Check if a tour code is valid
QBCore.Functions.CreateCallback('valic_housing:server:IsTourValid', function(source, cb, houseId, tourCode)
    cb(IsTourValid(houseId, tourCode))
end)

-- Purchase house
RegisterNetEvent('valic_housing:server:PurchaseHouse', function(houseId, password)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    if house.owner_citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House already owned',
            type = 'error'
        })
        return
    end

    -- Find house price from config
    local housePrice = 0
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            housePrice = configHouse.price
            break
        end
    end

    -- Check if player has enough money
    if player.PlayerData.money.cash < housePrice then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('not_enough_money'),
            type = 'error'
        })
        return
    end

    -- Check password validity
    if not IsValidPassword(password) then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('password_requirements', Config.MinPasswordLength, Config.MaxPasswordLength),
            type = 'error'
        })
        return
    end

    -- Remove money from player
    player.Functions.RemoveMoney('cash', housePrice)

    -- Update house in database
    local success = PurchaseHouse(houseId, player.PlayerData.citizenid, password, house.property_name)

    if success then
        -- Log purchase
        LogHousePurchase(src, houseId, house.property_name, housePrice)

        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('property_purchased', housePrice),
            type = 'success'
        })

        -- Update client
        TriggerClientEvent('valic_housing:client:HousePurchased', src, houseId)
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        -- Refund money if purchase failed
        player.Functions.AddMoney('cash', housePrice)

        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to purchase house',
            type = 'error'
        })
    end
end)

-- Sell house
RegisterNetEvent('valic_housing:server:SellHouse', function(houseId)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Check if player is the owner
    if house.owner_citizenid ~= player.PlayerData.citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'You do not own this house',
            type = 'error'
        })
        return
    end

    -- Find house price from config
    local housePrice = 0
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            housePrice = configHouse.price
            break
        end
    end

    -- Calculate sell price (e.g., 80% of purchase price)
    local sellPrice = math.floor(housePrice * 0.8)

    -- Add money to player
    player.Functions.AddMoney('cash', sellPrice)

    -- Update house in database
    local success = SellHouse(houseId)

    if success then
        -- Log sale
        LogHouseSale(src, houseId, house.property_name, sellPrice)

        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('property_sold', sellPrice),
            type = 'success'
        })

        -- Clear house storage if ox_inventory is available
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            if exports.ox_inventory.clearHouseStorage then
                exports.ox_inventory.clearHouseStorage(houseId)
            end
        end

        -- Update client
        TriggerClientEvent('valic_housing:client:HouseSold', src, houseId)
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        -- Take back money if sale failed
        player.Functions.RemoveMoney('cash', sellPrice)

        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to sell house',
            type = 'error'
        })
    end
end)

-- Transfer house
RegisterNetEvent('valic_housing:server:TransferHouse', function(houseId, targetId)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    local target = QBCore.Functions.GetPlayer(targetId)

    if not player or not target then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Player not found',
            type = 'error'
        })
        return
    end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Check if player is the owner
    if house.owner_citizenid ~= player.PlayerData.citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'You do not own this house',
            type = 'error'
        })
        return
    end

    -- Update house in database
    local success = TransferHouse(houseId, target.PlayerData.citizenid)

    if success then
        -- Log transfer
        LogHouseTransfer(src, targetId, houseId, house.property_name)

        -- Notify players
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('property_transferred'),
            type = 'success'
        })

        TriggerClientEvent('ox_lib:notify', targetId, {
            title = _('notification_title'),
            description = 'You have received ownership of a property: ' .. house.property_name,
            type = 'success'
        })

        -- Update clients
        TriggerClientEvent('valic_housing:client:HouseTransferred', src, houseId)
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to transfer house',
            type = 'error'
        })
    end
end)

-- Change house password
RegisterNetEvent('valic_housing:server:ChangePassword', function(houseId, newPassword)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Log password change
    LogPasswordChange(src, houseId, house.property_name)

    -- Check if player is the owner or has keys
    local isOwner = house.owner_citizenid == player.PlayerData.citizenid
    local hasKeys = HasHouseKeys(houseId, player.PlayerData.citizenid)

    if not isOwner and not hasKeys then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'You do not have permission to change the password',
            type = 'error'
        })
        return
    end

    -- Check password validity
    if not IsValidPassword(newPassword) then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('password_requirements', Config.MinPasswordLength, Config.MaxPasswordLength),
            type = 'error'
        })
        return
    end

    -- Update house in database
    local success = UpdateHousePassword(houseId, newPassword)

    if success then
        -- Log password change
        LogPasswordChange(src, houseId, house.property_name)

        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('password_changed'),
            type = 'success'
        })

        -- Reset password attempts
        ResetPasswordAttempts(houseId)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to change password',
            type = 'error'
        })
    end
end)

-- Rename house
RegisterNetEvent('valic_housing:server:RenameHouse', function(houseId, newName)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Check if player is the owner
    if house.owner_citizenid ~= player.PlayerData.citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'You do not own this house',
            type = 'error'
        })
        return
    end

    -- Validate name
    if not newName or newName == '' or string.len(newName) > 100 then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Invalid property name',
            type = 'error'
        })
        return
    end

    -- Update house in database
    local success = RenameHouse(houseId, newName)

    if success then
        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('property_renamed', newName),
            type = 'success'
        })

        -- Update client
        TriggerClientEvent('valic_housing:client:HouseRenamed', src, houseId, newName)
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to rename house',
            type = 'error'
        })
    end
end)

-- Share keys
RegisterNetEvent('valic_housing:server:ShareKeys', function(houseId, targetId)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    local target = QBCore.Functions.GetPlayer(targetId)

    if not player or not target then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Player not found',
            type = 'error'
        })
        return
    end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Check if player is the owner
    if house.owner_citizenid ~= player.PlayerData.citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'You do not own this house',
            type = 'error'
        })
        return
    end

    -- Add key holder in database
    local success = AddKeyHolder(houseId, target.PlayerData.citizenid)

    if success then
        -- Log key sharing
        LogKeySharing(src, targetId, houseId, house.property_name, "add")

        -- Notify players
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('keys_shared', target.PlayerData.charinfo.firstname .. ' ' .. target.PlayerData.charinfo.lastname),
            type = 'success'
        })

        TriggerClientEvent('ox_lib:notify', targetId, {
            title = _('notification_title'),
            description = 'You have received keys to: ' .. house.property_name,
            type = 'success'
        })

        -- Update client
        TriggerClientEvent('valic_housing:client:KeysShared', targetId, houseId)
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to share keys',
            type = 'error'
        })
    end
end)

-- Revoke keys
RegisterNetEvent('valic_housing:server:RevokeKeys', function(houseId, targetCitizenId)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Check if player is the owner
    if house.owner_citizenid ~= player.PlayerData.citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'You do not own this house',
            type = 'error'
        })
        return
    end

    -- Find target player name for notification
    local targetName = "Player"
    for _, p in pairs(QBCore.Functions.GetPlayers()) do
        local targetPlayer = QBCore.Functions.GetPlayer(p)
        if targetPlayer and targetPlayer.PlayerData.citizenid == targetCitizenId then
            targetName = targetPlayer.PlayerData.charinfo.firstname .. ' ' .. targetPlayer.PlayerData.charinfo.lastname
            break
        end
    end

    -- Remove key holder from database
    local success = RemoveKeyHolder(houseId, targetCitizenId)

    if success then
        -- Log key revocation
        LogKeyRevocation(src, targetCitizenId, houseId, house.property_name)

        -- Notify target player if online
        for _, p in pairs(QBCore.Functions.GetPlayers()) do
            local targetPlayer = QBCore.Functions.GetPlayer(p)
            if targetPlayer and targetPlayer.PlayerData.citizenid == targetCitizenId then
                -- Notify target player
                TriggerClientEvent('ox_lib:notify', p, {
                    title = 'Valic Housing',
                    description = 'Your keys to ' .. house.property_name .. ' have been revoked',
                    type = 'error'
                })

                -- Update target client
                TriggerClientEvent('valic_housing:client:KeysRevoked', p, houseId)
                break
            end
        end

        -- Notify owner
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Valic Housing',
            description = 'Klíče odebrány: ' .. targetName,
            type = 'success'
        })

        -- Update all clients
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Valic Housing',
            description = 'Failed to revoke keys',
            type = 'error'
        })
    end
end)

-- Start property tour
RegisterNetEvent('valic_housing:server:StartTour', function(houseId)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'House not found',
            type = 'error'
        })
        return
    end

    -- Check if house is already owned
    if house.owner_citizenid then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'This house is already owned',
            type = 'error'
        })
        return
    end

    -- Generate tour code
    local tourCode = GenerateNumericCode(Config.TourCodeLength)

    -- Set tour code in database
    local success, expiry = SetTourCode(houseId, tourCode)

    if success then
        -- Update door passcodes in ox_doorlock
        if house.door_ids then
            -- Parse door IDs
            local doorIds
            local parseSuccess, errorMsg = pcall(function()
                doorIds = json.decode(house.door_ids) or {}
            end)

            if not parseSuccess then
                doorIds = {}
            end

            -- Update passcode for each door in ox_doorlock
            for _, doorId in ipairs(doorIds) do
                -- Ensure doorId is a number
                doorId = tonumber(doorId)
                if doorId then
                    -- Update door passcode in ox_doorlock database directly
                    local doorResult = MySQL.Sync.fetchAll('SELECT * FROM ox_doorlock WHERE id = ?', {doorId})
                    if doorResult and doorResult[1] then
                        local doorData
                        local parseSuccess, errorMsg = pcall(function()
                            doorData = json.decode(doorResult[1].data)
                        end)

                        if parseSuccess and doorData then
                            -- Update passcode
                            doorData.passcode = tourCode

                            -- Use new ox_doorlock export for dynamic updates
                            if exports.ox_doorlock.updateDoorPasscode then
                                exports.ox_doorlock.updateDoorPasscode(doorId, tourCode)
                            else
                                -- Fallback to direct database update
                                local dbSuccess = MySQL.Sync.execute('UPDATE ox_doorlock SET data = ? WHERE id = ?',
                                    {json.encode(doorData), doorId}
                                )

                                -- Trigger event to update all clients
                                TriggerEvent('ox_doorlock:editDoorlock', doorId, doorData)
                            end
                        end
                    end
                end
            end
        end

        -- Log tour start
        LogPropertyTour(src, houseId, house.property_name, "start", tourCode)

        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('tour_started', tourCode, Config.TourDuration),
            type = 'success'
        })

        -- Start tour on client
        TriggerClientEvent('valic_housing:client:TourStarted', src, houseId, tourCode, expiry)
    else

        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to start tour',
            type = 'error'
        })
    end
end)

-- End property tour
RegisterNetEvent('valic_housing:server:EndTour', function(houseId)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local house = GetHouseById(houseId)
    if not house then
        return
    end

    -- Clear tour code in database
    local success = ClearTourCode(houseId)

    if success then
        -- Generate new random passcode for doors
        local newPasscode = GenerateNumericCode(Config.TourCodeLength)

        -- Reset door passcodes in ox_doorlock to the new random passcode
        if house.door_ids then
            -- Parse door IDs
            local doorIds
            local parseSuccess, errorMsg = pcall(function()
                doorIds = json.decode(house.door_ids) or {}
            end)

            if not parseSuccess then
                doorIds = {}
            end

            -- Update passcode for each door in ox_doorlock
            for _, doorId in ipairs(doorIds) do
                -- Ensure doorId is a number
                doorId = tonumber(doorId)
                if doorId then
                    -- Update door passcode in ox_doorlock database directly
                    local doorResult = MySQL.Sync.fetchAll('SELECT * FROM ox_doorlock WHERE id = ?', {doorId})
                    if doorResult and doorResult[1] then
                        local doorData
                        local parseSuccess, errorMsg = pcall(function()
                            doorData = json.decode(doorResult[1].data)
                        end)

                        if parseSuccess and doorData then
                            -- Update passcode to new random code
                            doorData.passcode = newPasscode

                            -- Use new ox_doorlock export for dynamic updates
                            if exports.ox_doorlock.updateDoorPasscode then
                                exports.ox_doorlock.updateDoorPasscode(doorId, newPasscode)
                            else
                                -- Fallback to direct database update
                                local dbSuccess = MySQL.Sync.execute('UPDATE ox_doorlock SET data = ? WHERE id = ?',
                                    {json.encode(doorData), doorId}
                                )

                                -- Trigger event to update all clients
                                TriggerEvent('ox_doorlock:editDoorlock', doorId, doorData)
                            end
                        end
                    end
                end
            end
        end

        -- Log tour end
        LogPropertyTour(src, houseId, house.property_name, "end")

        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = _('tour_ended'),
            type = 'success'
        })

        -- End tour on client
        TriggerClientEvent('valic_housing:client:TourEnded', src, houseId)
    else

        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Failed to end tour',
            type = 'error'
        })
    end
end)

-- Reset all houses (admin command)
QBCore.Commands.Add('resetallhouses', 'Reset all houses (Admin Only)', {}, false, function(source, args)
    local src = source
    local player = QBCore.Functions.GetPlayer(src)

    -- Check if player is admin
    if not player.PlayerData.metadata.admin then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Nemáte oprávnění k použití tohoto příkazu',
            type = 'error'
        })
        return
    end

    -- Confirm reset
    TriggerClientEvent('valic_housing:client:ConfirmResetAllHouses', src)
end)

-- Reset all houses (server event)
RegisterNetEvent('valic_housing:server:ResetAllHouses', function()
    local src = source
    local player = QBCore.Functions.GetPlayer(src)

    -- Check if player is admin
    if not player.PlayerData.metadata.admin then
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Nemáte oprávnění k použití tohoto příkazu',
            type = 'error'
        })
        return
    end

    -- Reset all houses in database
    local success = MySQL.Sync.execute('UPDATE valic_houses SET owner_citizenid = NULL, password = NULL, key_holders = NULL, tour_code = NULL, tour_expiry = NULL')

    if success then
        -- Log the reset
        print('^2[valic_housing]^7 All houses have been reset by admin: ' .. player.PlayerData.name)

        -- Use the new admin action logging
        LogAdminAction(src, "Reset All Houses", "All properties have been reset to their default state")

        -- Notify player
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Všechny domy byly resetovány',
            type = 'success'
        })

        -- Clear all house storage if ox_inventory is available
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            if exports.ox_inventory.clearHouseStorage then
                local houses = GetAllHouses()
                for _, house in pairs(houses) do
                    exports.ox_inventory.clearHouseStorage(house.id)
                end
            end
        end

        -- Refresh houses for all clients
        TriggerClientEvent('valic_housing:client:RefreshHouses', -1)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = _('notification_title'),
            description = 'Nepodařilo se resetovat domy',
            type = 'error'
        })
    end
end)

-- Exports for other resources
exports('IsHouseOwner', function(houseId, citizenId)
    return IsHouseOwner(houseId, citizenId)
end)

exports('HasHouseKeys', function(houseId, citizenId)
    return HasHouseKeys(houseId, citizenId)
end)

exports('GetHouseById', function(houseId)
    return GetHouseById(houseId)
end)

exports('GetAllHouses', function()
    return GetAllHouses()
end)

exports('GetHousesByCitizenId', function(citizenId)
    return GetHousesByCitizenId(citizenId)
end)

exports('GetHousesWithKeys', function(citizenId)
    return GetHousesWithKeys(citizenId)
end)

exports('CanAccessHouse', function(houseId, citizenId, tourCode)
    return CanAccessHouse(houseId, citizenId, tourCode)
end)

exports('IsTourValid', function(houseId, tourCode)
    return IsTourValid(houseId, tourCode)
end)

-- Note: Door passcode updates are now handled directly in the database functions
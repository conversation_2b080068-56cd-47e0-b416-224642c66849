Config = {}

-- General settings
Config.Debug = false -- Enable debug mode
Config.Locale = 'cs' -- Language (cs for Czech)

-- Discord webhook settings
-- Note: Webhook URL is now configured in server/config.lua and loaded from server.cfg
-- This is more secure as it doesn't expose the webhook URL in client-side code
Config.UseWebhook = true -- This is just a fallback, actual setting is in server config

-- Password settings
Config.MinPasswordLength = 4
Config.MaxPasswordLength = 12
Config.MaxPasswordAttempts = 3
Config.PasswordLockoutTime = 10 -- minutes

-- Property tour settings
Config.TourDuration = 10 -- minutes
Config.TourCodeLength = 4

-- Blip settings
Config.Blips = {
    OwnedProperty = {
        Sprite = 40,
        Color = 3,
        Scale = 0.7,
        Display = 4,
        ShortRange = true
    },
    SharedProperty = {
        Sprite = 40,
        Color = 5,
        Scale = 0.7,
        Display = 4,
        ShortRange = true
    },
    AvailableProperty = {
        Sprite = 40,
        Color = 2,
        Scale = 0.7,
        Display = 4,
        ShortRange = true
    }
}

-- NPC settings
Config.NPCModel = 'a_m_m_business_01'
Config.NPCScenario = 'WORLD_HUMAN_CLIPBOARD'

Config.Houses = {
    -- Example house configuration
    -- Each house needs:
    -- 1. A unique ID
    -- 2. A name
    -- 3. A price
    -- 4. NPC position for when the house is available
    -- 5. Admin panel position for when the house is owned
    -- 6. Array of door IDs that are controlled by this house
    
    {
        id = 1,
        name = "test barak",
        price = 500000,
        npc = {
            coords = vec4(-2539.74, 3756.94, 13.12, 260.37), -- x, y, z, heading
            scenario = "WORLD_HUMAN_CLIPBOARD"
        },
        adminPanel = {
            coords = vec3(-2547.61, 3750.27, 13.42)
        },
        doorIds = {7, 8} -- IDs from ox_doorlock
    }    
    -- Add more houses as needed
}

-- Database operations for valic_housing

local QBCore = exports['qb-core']:GetCoreObject()

-- Load config
Config = {}
Config.Debug = false
Config.TourDuration = 10

-- Load houses config
Config.Houses = {}
local configFile = LoadResourceFile(GetCurrentResourceName(), 'config/houses.lua')
if configFile then
    local func, err = load(configFile)
    if func then
        func()
    else
        print('^1[ERROR] Failed to load houses config: ' .. err .. '^7')
    end
end

-- Initialize houses in the database
local function InitializeHouses()
    local houses = Config.Houses

    for _, house in pairs(houses) do
        local doorIdsJson = json.encode(house.doorIds)

        -- Check if house already exists in the database
        local result = MySQL.Sync.fetchScalar('SELECT COUNT(*) FROM valic_houses WHERE id = ?', {house.id})

        if result == 0 then
            -- Insert new house
            MySQL.Async.execute('INSERT INTO valic_houses (id, property_name, door_ids) VALUES (?, ?, ?)',
                {house.id, house.name, doorIdsJson}
            )
            if Config.Debug then
                print('^2[valic_housing]^7 Added house to database: ' .. house.name)
            end
        else
            -- Update existing house
            MySQL.Async.execute('UPDATE valic_houses SET property_name = ?, door_ids = ? WHERE id = ?',
                {house.name, doorIdsJson, house.id}
            )
            if Config.Debug then
                print('^2[valic_housing]^7 Updated house in database: ' .. house.name)
            end
        end
    end

    print('^2[valic_housing]^7 Houses initialized in database')
end

-- Get all houses from database
function GetAllHouses()
    local houses = MySQL.Sync.fetchAll('SELECT * FROM valic_houses', {})
    return houses
end

-- Get house by ID
function GetHouseById(houseId)
    local house = MySQL.Sync.fetchAll('SELECT * FROM valic_houses WHERE id = ?', {houseId})
    if house and house[1] then
        return house[1]
    end
    return nil
end

-- Get houses owned by citizen ID
function GetHousesByCitizenId(citizenId)
    local houses = MySQL.Sync.fetchAll('SELECT * FROM valic_houses WHERE owner_citizenid = ?', {citizenId})
    return houses
end

-- Get houses where player has keys
function GetHousesWithKeys(citizenId)
    local houses = MySQL.Sync.fetchAll('SELECT * FROM valic_houses WHERE JSON_CONTAINS(key_holders, ?)', {'"' .. citizenId .. '"'})
    return houses
end

-- Purchase a house
function PurchaseHouse(houseId, citizenId, password, propertyName)
    -- Update house in valic_houses table
    local success = MySQL.Sync.execute('UPDATE valic_houses SET owner_citizenid = ?, password = ?, property_name = ? WHERE id = ?',
        {citizenId, password, propertyName, houseId}
    )

    -- Also update door passcodes in ox_doorlock
    if success > 0 then
        -- Get door IDs for this house
        local house = GetHouseById(houseId)
        if house and house.door_ids then
            -- Parse door IDs
            local doorIds = json.decode(house.door_ids) or {}

            -- Update passcode for each door in ox_doorlock
            for _, doorId in ipairs(doorIds) do
                -- Ensure doorId is a number
                doorId = tonumber(doorId)
                if doorId then
                    -- Update door passcode in ox_doorlock database directly
                    local doorResult = MySQL.Sync.fetchAll('SELECT * FROM ox_doorlock WHERE id = ?', {doorId})
                    if doorResult and doorResult[1] then
                        local doorData = json.decode(doorResult[1].data)
                        if doorData then
                            -- Update passcode
                            doorData.passcode = password

                            -- Use new ox_doorlock export for dynamic updates
                            if exports.ox_doorlock.updateDoorPasscode then
                                exports.ox_doorlock.updateDoorPasscode(doorId, password)
                            else
                                -- Fallback to direct database update
                                MySQL.Sync.execute('UPDATE ox_doorlock SET data = ? WHERE id = ?',
                                    {json.encode(doorData), doorId}
                                )

                                -- Trigger event to update all clients
                                TriggerEvent('ox_doorlock:editDoorlock', doorId, doorData)
                            end
                        end
                    end
                end
            end
        end
    end

    return success > 0
end

-- Sell/Reset a house
function SellHouse(houseId)
    -- Get house data before resetting
    local house = GetHouseById(houseId)

    -- Reset house in valic_houses table
    local success = MySQL.Sync.execute('UPDATE valic_houses SET owner_citizenid = NULL, password = NULL, key_holders = NULL, tour_code = NULL, tour_expiry = NULL WHERE id = ?',
        {houseId}
    )

    -- Reset door passcodes in ox_doorlock
    if success > 0 and house and house.door_ids then
        -- Parse door IDs
        local doorIds = json.decode(house.door_ids) or {}

        -- Reset passcode for each door in ox_doorlock
        for _, doorId in ipairs(doorIds) do
            -- Ensure doorId is a number
            doorId = tonumber(doorId)
            if doorId then
                -- Update door passcode in ox_doorlock database directly
                local doorResult = MySQL.Sync.fetchAll('SELECT * FROM ox_doorlock WHERE id = ?', {doorId})
                if doorResult and doorResult[1] then
                    local doorData = json.decode(doorResult[1].data)
                    if doorData then
                        -- Reset passcode to default "1"
                        doorData.passcode = "1"

                        -- Save updated door data
                        MySQL.Sync.execute('UPDATE ox_doorlock SET data = ? WHERE id = ?',
                            {json.encode(doorData), doorId}
                        )

                        -- Trigger event to update all clients
                        TriggerEvent('ox_doorlock:editDoorlock', doorId, doorData)
                    end
                end
            end
        end
    end

    return success > 0
end

-- Transfer house ownership
function TransferHouse(houseId, newCitizenId)
    local success = MySQL.Sync.execute('UPDATE valic_houses SET owner_citizenid = ?, key_holders = NULL WHERE id = ?',
        {newCitizenId, houseId}
    )
    return success > 0
end

-- Update house password
function UpdateHousePassword(houseId, password)
    print('^3[DEBUG]^7 UpdateHousePassword called for house ' .. houseId .. ' with password ' .. password)

    -- Update password in valic_houses table
    local success = MySQL.Sync.execute('UPDATE valic_houses SET password = ? WHERE id = ?',
        {password, houseId}
    )

    print('^3[DEBUG]^7 Updated password in valic_houses table: ' .. tostring(success > 0))

    -- Get door IDs for this house
    local house = GetHouseById(houseId)
    if not house then
        print('^1[DEBUG]^7 House not found: ' .. houseId)
        return success > 0
    end

    if not house.door_ids then
        print('^1[DEBUG]^7 No door_ids found for house: ' .. houseId)
        return success > 0
    end

    -- Parse door IDs
    local doorIds
    local parseSuccess, errorMsg = pcall(function()
        doorIds = json.decode(house.door_ids) or {}
    end)

    if not parseSuccess then
        print('^1[DEBUG]^7 Failed to parse door_ids for house ' .. houseId .. ': ' .. tostring(errorMsg))
        return success > 0
    end

    print('^3[DEBUG]^7 Found ' .. #doorIds .. ' doors for house ' .. houseId)

    -- Update passcode for each door in ox_doorlock
    for _, doorId in ipairs(doorIds) do
        -- Ensure doorId is a number
        doorId = tonumber(doorId)
        if doorId then
            print('^3[DEBUG]^7 Updating passcode for door ' .. doorId .. ' to ' .. password)

            -- Update door passcode in ox_doorlock database directly
            local doorResult = MySQL.Sync.fetchAll('SELECT * FROM ox_doorlock WHERE id = ?', {doorId})
            if doorResult and doorResult[1] then
                print('^3[DEBUG]^7 Found door in database: ' .. doorId)

                local doorData
                local parseSuccess, errorMsg = pcall(function()
                    doorData = json.decode(doorResult[1].data)
                end)

                if not parseSuccess then
                    print('^1[DEBUG]^7 Failed to parse door data for door ' .. doorId .. ': ' .. tostring(errorMsg))
                else
                    if doorData then
                        print('^3[DEBUG]^7 Current door data: ' .. json.encode(doorData))

                        -- Update passcode
                        doorData.passcode = password

                        -- Use new ox_doorlock export for dynamic updates
                        if exports.ox_doorlock.updateDoorPasscode then
                            local updateSuccess = exports.ox_doorlock.updateDoorPasscode(doorId, password)
                            print('^2[DEBUG]^7 Used ox_doorlock export to update door ' .. doorId .. ': ' .. tostring(updateSuccess))
                        else
                            -- Fallback to direct database update
                            local dbSuccess = MySQL.Sync.execute('UPDATE ox_doorlock SET data = ? WHERE id = ?',
                                {json.encode(doorData), doorId}
                            )

                            print('^3[DEBUG]^7 Updated door data in database: ' .. tostring(dbSuccess > 0))

                            -- Trigger event to update all clients
                            TriggerEvent('ox_doorlock:editDoorlock', doorId, doorData)
                            print('^2[DEBUG]^7 Triggered editDoorlock event for door ' .. doorId)
                        end
                    else
                        print('^1[DEBUG]^7 Door data is nil for door ' .. doorId)
                    end
                end
            else
                print('^1[DEBUG]^7 Door not found in database: ' .. doorId)
            end
        else
            print('^1[DEBUG]^7 Invalid door ID: ' .. tostring(doorId))
        end
    end

    return success > 0
end

-- Rename house
function RenameHouse(houseId, newName)
    local success = MySQL.Sync.execute('UPDATE valic_houses SET property_name = ? WHERE id = ?',
        {newName, houseId}
    )
    return success > 0
end

-- Add key holder
function AddKeyHolder(houseId, citizenId)
    local house = GetHouseById(houseId)
    if not house then return false end

    local keyHolders = {}
    if house.key_holders then
        keyHolders = json.decode(house.key_holders) or {}
    end

    -- Check if citizen already has keys
    for _, holder in ipairs(keyHolders) do
        if holder == citizenId then
            return true -- Already has keys
        end
    end

    -- Add new key holder
    table.insert(keyHolders, citizenId)

    local success = MySQL.Sync.execute('UPDATE valic_houses SET key_holders = ? WHERE id = ?',
        {json.encode(keyHolders), houseId}
    )
    return success > 0
end

-- Remove key holder
function RemoveKeyHolder(houseId, citizenId)
    local house = GetHouseById(houseId)
    if not house or not house.key_holders then return false end

    local keyHolders = json.decode(house.key_holders) or {}
    local newKeyHolders = {}

    -- Filter out the citizen ID
    for _, holder in ipairs(keyHolders) do
        if holder ~= citizenId then
            table.insert(newKeyHolders, holder)
        end
    end

    local success = MySQL.Sync.execute('UPDATE valic_houses SET key_holders = ? WHERE id = ?',
        {json.encode(newKeyHolders), houseId}
    )
    return success > 0
end

-- Set tour code and expiry
function SetTourCode(houseId, code)
    local expiry = os.time() + (Config.TourDuration * 60) -- Current time + tour duration in seconds
    local expiryFormatted = os.date('%Y-%m-%d %H:%M:%S', expiry)

    local success = MySQL.Sync.execute('UPDATE valic_houses SET tour_code = ?, tour_expiry = ? WHERE id = ?',
        {code, expiryFormatted, houseId}
    )
    return success > 0, expiry
end

-- Clear tour code
function ClearTourCode(houseId)
    local success = MySQL.Sync.execute('UPDATE valic_houses SET tour_code = NULL, tour_expiry = NULL WHERE id = ?',
        {houseId}
    )
    return success > 0
end

-- Check if tour is valid
function IsTourValid(houseId, code)
    local house = GetHouseById(houseId)
    if not house or not house.tour_code or not house.tour_expiry then return false end

    if house.tour_code ~= code then return false end

    local expiry = house.tour_expiry
    local currentTime = os.time()
    local expiryTime = os.time({
        year = tonumber(string.sub(expiry, 1, 4)),
        month = tonumber(string.sub(expiry, 6, 7)),
        day = tonumber(string.sub(expiry, 9, 10)),
        hour = tonumber(string.sub(expiry, 12, 13)),
        min = tonumber(string.sub(expiry, 15, 16)),
        sec = tonumber(string.sub(expiry, 18, 19))
    })

    return currentTime < expiryTime
end

-- Initialize houses when resource starts
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        InitializeHouses()
    end
end)
